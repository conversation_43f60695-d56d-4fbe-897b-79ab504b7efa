#!/bin/bash

# Car Metadata Admin Endpoints Testing Script
# Tests CRUD operations, authentication, and authorization for car metadata management

# Configuration
BASE_URL="http://localhost:8765/api/v1"
ADMIN_TOKEN="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJwYXlsb2FkIjp7ImVtcGxveWVlX2lkIjoidGVzdC1hZG1pbi1pZCIsInJvbGUiOiJBRE1JTiJ9LCJyb2xlIjoiQURNSU4iLCJleHAiOiIyMDI1LTA2LTMwVDA3OjI1OjA2LjY1MFoifQ.zt9JqseSujMCBEy6ftJZdYkN80GHGkxFWpyt-l0GSvo"
INVALID_TOKEN="invalid.token.here"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test result tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Helper functions
print_header() {
    echo -e "\n${BLUE}============================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}============================================${NC}"
}

print_test() {
    echo -e "\n${YELLOW}🧪 Test: $1${NC}"
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
}

print_success() {
    echo -e "${GREEN}✅ PASS: $1${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
}

print_fail() {
    echo -e "${RED}❌ FAIL: $1${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
}

check_response() {
    local expected_status=$1
    local actual_status=$2
    local test_name=$3
    
    if [ "$actual_status" = "$expected_status" ]; then
        print_success "$test_name (Status: $actual_status)"
    else
        print_fail "$test_name (Expected: $expected_status, Got: $actual_status)"
    fi
}

# Global variables to store created IDs
MANUFACTURER_ID=""
MODEL_ID=""
OPTION_GROUP_ID=""
OPTION_ID=""

print_header "🚀 STARTING CAR METADATA ENDPOINTS TESTING"

# ============================================
# 1. AUTHENTICATION TESTS
# ============================================
print_header "🔐 AUTHENTICATION TESTS"

print_test "Access without token should return 401"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X GET "$BASE_URL/car-manufacturers")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "401" "$status" "No token authentication"

print_test "Access with invalid token should return 401"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $INVALID_TOKEN" -X GET "$BASE_URL/car-manufacturers")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "401" "$status" "Invalid token authentication"

print_test "Access with valid admin token should return 200"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" -X GET "$BASE_URL/car-manufacturers")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "200" "$status" "Valid admin token authentication"

# ============================================
# 2. CAR MANUFACTURERS CRUD TESTS
# ============================================
print_header "🏭 CAR MANUFACTURERS CRUD TESTS"

print_test "Create car manufacturer with valid data"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "Content-Type: application/json" \
    -X POST "$BASE_URL/car-manufacturers" \
    -d '{
        "name": "Test Motors",
        "country": "South Korea",
        "logo_url": "https://example.com/logo.png",
        "website_url": "https://testmotors.com",
        "description": "A test car manufacturer"
    }')
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
body=$(echo $response | sed 's/HTTPSTATUS:[0-9]*//g')
MANUFACTURER_ID=$(echo $body | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
check_response "201" "$status" "Create manufacturer"
echo "Created manufacturer ID: $MANUFACTURER_ID"

print_test "Create manufacturer with missing required field should return 400"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "Content-Type: application/json" \
    -X POST "$BASE_URL/car-manufacturers" \
    -d '{"country": "Japan"}')
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "400" "$status" "Missing required field validation"

print_test "Get all manufacturers"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -X GET "$BASE_URL/car-manufacturers")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "200" "$status" "Get all manufacturers"

print_test "Get specific manufacturer by ID"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -X GET "$BASE_URL/car-manufacturers/$MANUFACTURER_ID")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "200" "$status" "Get manufacturer by ID"

print_test "Get manufacturer with invalid UUID should return 400"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -X GET "$BASE_URL/car-manufacturers/invalid-uuid")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "400" "$status" "Invalid UUID validation"

print_test "Update manufacturer"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "Content-Type: application/json" \
    -X PUT "$BASE_URL/car-manufacturers/$MANUFACTURER_ID" \
    -d '{
        "name": "Updated Test Motors",
        "country": "South Korea",
        "description": "Updated description"
    }')
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "200" "$status" "Update manufacturer"

# ============================================
# 3. CAR MODELS CRUD TESTS
# ============================================
print_header "🚗 CAR MODELS CRUD TESTS"

print_test "Create car model with valid manufacturer"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "Content-Type: application/json" \
    -X POST "$BASE_URL/car-models" \
    -d "{
        \"name\": \"Test Model X\",
        \"manufacturer_id\": \"$MANUFACTURER_ID\",
        \"body_type\": \"Sedan\",
        \"fuel_type\": \"Gasoline\",
        \"start_year\": 2020,
        \"end_year\": 2024,
        \"description\": \"A test car model\"
    }")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
body=$(echo $response | sed 's/HTTPSTATUS:[0-9]*//g')
MODEL_ID=$(echo $body | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
check_response "201" "$status" "Create car model"
echo "Created model ID: $MODEL_ID"

print_test "Create model with invalid manufacturer_id should return 400"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "Content-Type: application/json" \
    -X POST "$BASE_URL/car-models" \
    -d '{
        "name": "Invalid Model",
        "manufacturer_id": "00000000-0000-0000-0000-000000000000",
        "body_type": "SUV"
    }')
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "400" "$status" "Invalid manufacturer_id validation"

print_test "Get all car models"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -X GET "$BASE_URL/car-models")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "200" "$status" "Get all car models"

print_test "Get specific car model by ID"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -X GET "$BASE_URL/car-models/$MODEL_ID")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "200" "$status" "Get car model by ID"

print_test "Update car model"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "Content-Type: application/json" \
    -X PUT "$BASE_URL/car-models/$MODEL_ID" \
    -d "{
        \"name\": \"Updated Test Model X\",
        \"manufacturer_id\": \"$MANUFACTURER_ID\",
        \"body_type\": \"SUV\",
        \"fuel_type\": \"Hybrid\"
    }")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "200" "$status" "Update car model"

# ============================================
# 4. CAR OPTION GROUPS CRUD TESTS
# ============================================
print_header "🎛️ CAR OPTION GROUPS CRUD TESTS"

print_test "Create car option group"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "Content-Type: application/json" \
    -X POST "$BASE_URL/car-option-groups" \
    -d '{
        "name": "Safety Features",
        "description": "Safety-related car options",
        "display_order": 1,
        "icon": "🛡️",
        "color": "#FF5722"
    }')
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
body=$(echo $response | sed 's/HTTPSTATUS:[0-9]*//g')
OPTION_GROUP_ID=$(echo $body | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
check_response "201" "$status" "Create option group"
echo "Created option group ID: $OPTION_GROUP_ID"

print_test "Create option group with missing required field should return 400"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "Content-Type: application/json" \
    -X POST "$BASE_URL/car-option-groups" \
    -d '{"description": "Missing name field"}')
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "400" "$status" "Missing required field validation for option group"

print_test "Get all option groups"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -X GET "$BASE_URL/car-option-groups")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "200" "$status" "Get all option groups"

print_test "Get specific option group by ID"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -X GET "$BASE_URL/car-option-groups/$OPTION_GROUP_ID")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "200" "$status" "Get option group by ID"

print_test "Update option group"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "Content-Type: application/json" \
    -X PUT "$BASE_URL/car-option-groups/$OPTION_GROUP_ID" \
    -d '{
        "name": "Advanced Safety Features",
        "description": "Updated safety features description",
        "display_order": 2,
        "icon": "🛡️",
        "color": "#4CAF50"
    }')
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "200" "$status" "Update option group"

# ============================================
# 5. CAR OPTIONS CRUD TESTS
# ============================================
print_header "⚙️ CAR OPTIONS CRUD TESTS"

print_test "Create car option with valid option group"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "Content-Type: application/json" \
    -X POST "$BASE_URL/car-options" \
    -d "{
        \"name\": \"Anti-lock Braking System (ABS)\",
        \"option_group_id\": \"$OPTION_GROUP_ID\",
        \"description\": \"Prevents wheel lockup during braking\",
        \"is_premium\": false,
        \"price_impact\": 500.00
    }")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
body=$(echo $response | sed 's/HTTPSTATUS:[0-9]*//g')
OPTION_ID=$(echo $body | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
check_response "201" "$status" "Create car option"
echo "Created option ID: $OPTION_ID"

print_test "Create option with invalid option_group_id should return 400"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "Content-Type: application/json" \
    -X POST "$BASE_URL/car-options" \
    -d '{
        "name": "Invalid Option",
        "option_group_id": "00000000-0000-0000-0000-000000000000",
        "description": "This should fail"
    }')
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "400" "$status" "Invalid option_group_id validation"

print_test "Get all car options"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -X GET "$BASE_URL/car-options")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "200" "$status" "Get all car options"

print_test "Get specific car option by ID"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -X GET "$BASE_URL/car-options/$OPTION_ID")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "200" "$status" "Get car option by ID"

print_test "Update car option"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "Content-Type: application/json" \
    -X PUT "$BASE_URL/car-options/$OPTION_ID" \
    -d "{
        \"name\": \"Advanced ABS System\",
        \"option_group_id\": \"$OPTION_GROUP_ID\",
        \"description\": \"Updated ABS description\",
        \"is_premium\": true,
        \"price_impact\": 750.00
    }")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "200" "$status" "Update car option"

# ============================================
# 6. RELATIONSHIP CONSTRAINT TESTS
# ============================================
print_header "🔗 RELATIONSHIP CONSTRAINT TESTS"

print_test "Try to delete manufacturer with associated models should return 409"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -X DELETE "$BASE_URL/car-manufacturers/$MANUFACTURER_ID")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "409" "$status" "Delete manufacturer with models constraint"

print_test "Try to delete option group with associated options should return 409"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -X DELETE "$BASE_URL/car-option-groups/$OPTION_GROUP_ID")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "409" "$status" "Delete option group with options constraint"

# ============================================
# 7. CLEANUP TESTS
# ============================================
print_header "🧹 CLEANUP TESTS"

print_test "Delete car option"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -X DELETE "$BASE_URL/car-options/$OPTION_ID")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "200" "$status" "Delete car option"

print_test "Delete car model"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -X DELETE "$BASE_URL/car-models/$MODEL_ID")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "200" "$status" "Delete car model"

print_test "Delete option group after options removed"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -X DELETE "$BASE_URL/car-option-groups/$OPTION_GROUP_ID")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "200" "$status" "Delete option group"

print_test "Delete manufacturer after models removed"
response=$(curl -s -w "HTTPSTATUS:%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    -X DELETE "$BASE_URL/car-manufacturers/$MANUFACTURER_ID")
status=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
check_response "200" "$status" "Delete manufacturer"

# ============================================
# TEST SUMMARY
# ============================================
print_header "📊 TEST SUMMARY"
echo -e "Total Tests: ${BLUE}$TOTAL_TESTS${NC}"
echo -e "Passed: ${GREEN}$PASSED_TESTS${NC}"
echo -e "Failed: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 ALL TESTS PASSED! 🎉${NC}"
    echo -e "${GREEN}Car metadata admin endpoints are working correctly.${NC}"
    exit 0
else
    echo -e "\n${RED}❌ Some tests failed. Please review the results above.${NC}"
    exit 1
fi 